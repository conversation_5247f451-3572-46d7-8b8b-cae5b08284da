<!-- 专家出诊屏 -->
<template>
  <div class="page-container">
    <div class="page-content-container">
      <!-- 专家排班表格 -->
      <div class="schedule-container">
        <!-- 横幅标题 -->
        <table class="schedule-table">
          <tbody>
            <tr>
              <td colspan="15" class="banner-title">门诊医生出诊一览表</td>
            </tr>
            <!-- 10行数据 -->
            <tr v-for="rowIndex in 10" :key="`row-${rowIndex}`">
              <!-- 每行5个人，每人3列 -->
              <template v-for="personIndex in 5">
                <td :key="`dept-${rowIndex}-${personIndex}`" class="department-cell">
                  {{ getPersonData(rowIndex, personIndex, "DepartmentName") }}
                </td>
                <td :key="`doctor-${rowIndex}-${personIndex}`" class="doctor-cell">
                  {{ getPersonData(rowIndex, personIndex, "DoctorN<PERSON>") }}
                </td>
                <td :key="`location-${rowIndex}-${personIndex}`" class="location-cell">
                  {{ getPersonData(rowIndex, personIndex, "AdmitAddress") }}
                </td>
              </template>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- 医生信息 -->
      <div class="doctor-info">
        <div class="doctor-profile">
          <!-- 头像和基本信息区域 -->
          <div class="doctor-avatar-section">
            <div class="doctor-avatar">
              <img
                :src="doctorObj.DoctorImg"
                :alt="doctorObj.DoctorName"
                @error="handleImageError"
                class="avatar-img"
              />
            </div>
            <!-- 头像右侧的基本信息 -->
            <div class="doctor-basic-info">
              <div class="detail-item-row">
                <span class="label">名称：</span>
                <span class="value">{{ doctorObj.DoctorName }}</span>
              </div>
              <div class="detail-item-row">
                <span class="label">职称：</span>
                <span class="value">{{ doctorObj.DoctorLevel }}</span>
              </div>
              <div class="detail-item-row">
                <span class="label">科室：</span>
                <span class="value">{{ doctorObj.DeptName }}</span>
              </div>
            </div>
          </div>

          <!-- 详细信息区域 -->
          <div class="doctor-details">
            <div class="detail-item description-item">
              <span class="label">简介：</span>
              <div class="value-container">
                <div class="value">{{ doctorObj.Description }}</div>
              </div>
            </div>
            <div class="detail-item strength-item">
              <span class="label">专长：</span>
              <div class="value-container">
                <div class="value">{{ doctorObj.DoctorStrength }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { dataConversion } from "@/utils/index.js";
import dayjs from "dayjs";

export default {
  name: "specialistScreen",
  data() {
    return {
      // 科室列表
      departmentList: [],
      // 排班数据
      scheduleData: [],
      // 当前页码（从0开始）
      currentPage: 0,
      // 轮播定时器
      rotationTimer: null,
      // 数据刷新定时器
      refreshTimer: null,
      // 每页显示的数据条数
      itemsPerPage: 50,
      doctorObj: {
        DoctorCode: "128",
        DoctorName: "李义强",
        DoctotLevelCode: "1",
        DoctorLevel: "主任医师",
        DeptId: "188",
        DeptName: "骨科门诊",
        Description:
          "现任广东省医师协会脊柱外科医师分会、广东省康复医学会脊柱脊髓分会、广东省健康中国研究会脊柱专业委员会、韶关市康复医学会神经康复分会委员；主持韶关市科学基金2项、发表论文10余篇，副主编《临床骨科常见疾病诊治策略》",
        DoctorStrength:
          "脊柱退变性疾病的治疗(包括颈椎病，颈椎后纵韧带骨化症，腰椎间盘突出症，腰椎管狭窄症，腰椎滑脱症等)、骨质疏松患者的椎弓根螺钉固定及脊柱侧后凸畸形的矫形手术治疗。在脊柱微创内镜治疗方面有深入研究，曾在北京大学第三附属医院、广东省人民医院、广东省暨南大学附属医院、广东省中医院骨科精深学习脊柱微创技术；擅长各类脊柱微创手术如微创经皮椎弓根螺钉治疗胸腰椎骨折、经皮椎体成形、扩张通道下腰椎融合、椎间盘镜、椎间孔镜及单侧入路双通道内镜下髓核摘除、腰椎融合等。",
        DoctorImg: "https://172.16.8.8:1443/imedical/web/html/doctorimg/100527.png",
        InsuScore: "医保支付资格评分:总分12分,剩余12分 \\n 医保支付状态:正常",
      },
    };
  },
  created() {
    this.getScheduleData();
  },
  mounted() {
    // 如果数据超过50条，启动轮播
    this.startRotation();
    // 启动定时刷新功能
    this.startScheduleRefresh();
    // 输出调试信息
    console.log("页面挂载完成，数据总数:", this.scheduleData.length);
    console.log("每页显示:", this.itemsPerPage, "条");
    console.log("总页数:", Math.ceil(this.scheduleData.length / this.itemsPerPage));
  },

  methods: {
    // 获取排班数据
    async getScheduleData(isAutoRefresh = false) {
      try {
        // 记录刷新类型
        const refreshType = isAutoRefresh ? "定时自动刷新" : "页面初始化加载";
        console.log(`${refreshType} - 开始获取排班数据`, dayjs().format("YYYY-MM-DD HH:mm:ss"));

        const res = await this.$api.schedule.querySchedule();

        console.log(`${refreshType} - 排班接口返回数据`, res);

        if (res.success && res.data.ResultCode === "0") {
          // 处理返回的数据
          let scheduleItems = [];
          const items = dataConversion(res.data?.Schedules?.Schedule || []);

          // 过滤正常状态的排班数据
          scheduleItems = items.filter(item => item.ScheduleStatus === "N");

          this.scheduleData = scheduleItems;
          console.log(`${refreshType} - 成功加载了 ${this.scheduleData.length} 条排班数据`);

          // 如果是自动刷新且数据有变化，重新启动轮播
          if (isAutoRefresh && this.scheduleData.length > this.itemsPerPage) {
            this.restartRotation();
          }
        } else {
          const errorMsg = res.message || res.data?.ResultMessage || "未知错误";
          console.error(`${refreshType} - 排班接口返回错误:`, errorMsg);

          // 自动刷新失败时不清空现有数据，只记录错误
          if (!isAutoRefresh) {
            this.scheduleData = [];
          }
        }
      } catch (error) {
        const refreshType = isAutoRefresh ? "定时自动刷新" : "页面初始化加载";
        console.error(`${refreshType} - 获取排班数据失败:`, error);

        // 自动刷新失败时不清空现有数据，只记录错误
        if (!isAutoRefresh) {
          this.scheduleData = [];
        }
      }
    },
    // 获取指定位置的人员数据
    getPersonData(rowIndex, personIndex, field) {
      // 计算在当前页表格中的索引位置 (行索引-1) * 5 + (人员索引-1)
      const tableIndex = (rowIndex - 1) * 5 + (personIndex - 1);
      // 计算在整个数据集中的实际索引：当前页起始位置 + 表格内位置
      const dataIndex = this.currentPage * this.itemsPerPage + tableIndex;

      if (dataIndex < this.scheduleData.length && this.scheduleData[dataIndex]) {
        return this.scheduleData[dataIndex][field] || "";
      }
      return "";
    },
    // 启动数据轮播
    startRotation() {
      // 轮播时间
      const ROTATION_TIME = 60 * 1000;

      // 只有当数据超过一页（50条）时才启动轮播
      if (this.scheduleData.length > this.itemsPerPage) {
        this.rotationTimer = setInterval(() => {
          // 计算总页数
          const totalPages = Math.ceil(this.scheduleData.length / this.itemsPerPage);
          // 翻到下一页，如果是最后一页则回到第一页
          const oldPage = this.currentPage;
          this.currentPage = (this.currentPage + 1) % totalPages;
          console.log(`页面切换: ${oldPage + 1} → ${this.currentPage + 1} (共${totalPages}页)`);
        }, ROTATION_TIME);
      }
    },
    // 重新启动轮播（用于数据刷新后）
    restartRotation() {
      // 清除现有轮播定时器
      if (this.rotationTimer) {
        clearInterval(this.rotationTimer);
        this.rotationTimer = null;
      }
      // 重置到第一页
      this.currentPage = 0;
      // 重新启动轮播
      this.startRotation();
      console.log("数据刷新后重新启动轮播");
    },
    // 启动定时刷新功能
    startScheduleRefresh() {
      console.log("启动排班数据定时刷新功能");
      // 立即计算下一次刷新时间
      this.scheduleNextRefresh();
    },
    // 计算并设置下一次刷新时间
    scheduleNextRefresh() {
      const now = dayjs();
      const nextRefreshTime = this.getNextRefreshTime(now);
      const delayMs = nextRefreshTime.diff(now);

      console.log(`当前时间: ${now.format("YYYY-MM-DD HH:mm:ss")}`);
      console.log(`下次刷新时间: ${nextRefreshTime.format("YYYY-MM-DD HH:mm:ss")}`);
      console.log(`距离下次刷新还有: ${Math.round(delayMs / 1000 / 60)} 分钟`);

      // 设置定时器
      this.refreshTimer = setTimeout(() => {
        this.executeScheduleRefresh();
      }, delayMs);
    },
    // 获取下一次刷新时间
    getNextRefreshTime(currentTime) {
      // 定义刷新时间点：7:00 和 12:30
      const refreshTimes = [
        // { hour: 7, minute: 0 }, // 上午7点
        { hour: 1, minute: 1 }, // 上午7点
        { hour: 12, minute: 30 }, // 中午12点30分
      ];

      const today = currentTime.startOf("day");

      // 查找今天剩余的刷新时间点
      for (const timePoint of refreshTimes) {
        const refreshTime = today.hour(timePoint.hour).minute(timePoint.minute).second(0);
        if (refreshTime.isAfter(currentTime)) {
          return refreshTime;
        }
      }

      // 如果今天没有剩余的刷新时间点，返回明天的第一个刷新时间点
      const tomorrow = today.add(1, "day");
      return tomorrow.hour(refreshTimes[0].hour).minute(refreshTimes[0].minute).second(0);
    },
    // 执行定时刷新
    async executeScheduleRefresh() {
      console.log("执行定时刷新排班数据");

      try {
        // 调用数据刷新方法，标记为自动刷新
        await this.getScheduleData(true);
        console.log("定时刷新排班数据成功");
      } catch (error) {
        console.error("定时刷新排班数据失败:", error);
      } finally {
        // 无论成功失败，都要计划下一次刷新
        this.scheduleNextRefresh();
      }
    },
    // 获取当前页信息
    getCurrentPageInfo() {
      const totalPages = Math.ceil(this.scheduleData.length / this.itemsPerPage);
      const startIndex = this.currentPage * this.itemsPerPage;
      const endIndex = Math.min(startIndex + this.itemsPerPage, this.scheduleData.length);
      return {
        currentPage: this.currentPage + 1, // 显示时从1开始
        totalPages,
        startIndex: startIndex + 1, // 显示时从1开始
        endIndex,
        totalRecords: this.scheduleData.length,
      };
    },
    // 处理图片加载错误
    handleImageError(event) {
      console.log("图片加载失败", event);

      event.target.src = require("@/assets/images/default_avatar.png");
    },
  },
  beforeDestroy() {
    // 清理所有定时器
    if (this.rotationTimer) {
      clearInterval(this.rotationTimer);
    }
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }
  },
};
</script>

<style lang="scss" scoped>
$leftWidth: 75%;
$rightWidth: 25%;

$text-color-red: #ff0000;
$text-color-yellow: #ffff00;
$text-color-red-thin: #ff9999;
$border-color: #666666;
$table-cell-padding: 5px;

.page-container {
  background-color: #000000;
  padding: 0;
  .page-content-container {
    display: flex;
    height: 100%;
    .schedule-container {
      height: 100%;
      width: $leftWidth;

      .schedule-table {
        width: 100%;
        height: 100%;
        border-collapse: collapse;
        font-size: 14px;
        background-color: transparent; // 去掉表格背景色

        .banner-title {
          color: $text-color-red;
          text-align: center;
          font-size: 22px;
          font-weight: bold;
          letter-spacing: 40px;
        }

        th,
        td {
          border: 1px solid $border-color;
          padding: $table-cell-padding;
          text-align: center;
          vertical-align: middle;
          word-wrap: break-word;
          max-width: 120px;
          background-color: transparent; //  单元格去掉背景色，使用黑色背景
        }

        tbody {
          tr {
            background-color: transparent; // 去掉行背景色

            &:nth-child(even) {
              background-color: transparent;
            }
          }

          .department-cell {
            background-color: transparent;
            color: $text-color-red;
            border-right: 1px solid $border-color;
          }

          .doctor-cell {
            background-color: transparent;
            color: $text-color-yellow;
            border-right: 1px solid $border-color;
          }

          .location-cell {
            background-color: transparent;
            color: $text-color-red-thin;
            border-right: 1px solid $border-color;
          }
        }
      }
    }
    .doctor-info {
      width: $rightWidth;
      background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
      padding: 12px;
      position: relative;
      overflow: hidden;

      // 添加装饰性背景元素
      &::before {
        content: "";
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, rgba(45, 184, 235, 0.05) 0%, transparent 70%);
        pointer-events: none;
      }

      .doctor-profile {
        display: flex;
        flex-direction: column;
        height: 100%;
        position: relative;
        z-index: 1;

        .doctor-avatar-section {
          display: flex;
          flex-direction: row; // 改为横向布局
          align-items: stretch; // 顶部对齐
          margin-bottom: 15px;
          padding: 12px;
          background: rgba(255, 255, 255, 0.8);
          border-radius: 12px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          gap: 15px; // 头像和右侧信息之间的间距

          .doctor-avatar {
            width: 250px; // 适当缩小头像宽度以腾出空间
            height: 300px; // 适当缩小头像高度
            border-radius: 12px;
            overflow: hidden;
            flex-shrink: 0; // 防止头像被压缩

            .avatar-img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          // 头像右侧的基本信息区域
          .doctor-basic-info {
            flex: 1; // 占据剩余空间
            display: flex;
            flex-direction: column;
            justify-content: space-around;

            .detail-item-row {
              display: flex;
              align-items: center;
              background: rgba(255, 255, 255, 0.9);
              padding: 8px 12px;
              border-radius: 6px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
              border-left: 3px solid #2db8eb;

              .label {
                flex-shrink: 0;
                font-weight: 600;
                color: #2db8eb;
                font-size: 18px;
                position: relative;
                padding-left: 8px;

                &::before {
                  content: "";
                  position: absolute;
                  left: 0;
                  top: 50%;
                  transform: translateY(-50%);
                  width: 3px;
                  height: 12px;
                  background: linear-gradient(135deg, #2db8eb, #1e90ff);
                  border-radius: 2px;
                }
              }

              .value {
                flex: 1;
                font-size: 18px; // 稍微缩小字体
                color: #2c3e50;
                font-weight: 600;
                margin-left: 6px; // 稍微缩小左边距
              }
            }
          }
        }

        .doctor-details {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 12px;

          .label {
            font-weight: 700;
            color: #2db8eb;
            font-size: 18px;
            position: relative;
            padding-left: 12px;

            &::before {
              content: "";
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 4px;
              height: 16px;
              background: linear-gradient(135deg, #2db8eb, #1e90ff);
              border-radius: 2px;
            }
          }

          .detail-item {
            background: rgba(255, 255, 255, 0.9);
            padding: 12px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
            border-left: 4px solid #2db8eb;

            &.description-item,
            &.strength-item {
              .value-container {
                margin-top: 8px;
                background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
                border-radius: 6px;
                border: 1px solid rgba(45, 184, 235, 0.1);
                padding: 10px;
                position: relative;
                .value {
                  font-size: 16px;
                  color: #34495e;
                  line-height: 1.5;
                  text-align: justify;
                  // 文本内容直接应用截断，不需要额外容器
                  display: -webkit-box;
                  -webkit-line-clamp: 10;
                  line-clamp: 10;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
